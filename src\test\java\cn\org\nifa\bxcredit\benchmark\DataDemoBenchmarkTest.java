package cn.org.nifa.bxcredit.benchmark;

import cn.org.nifa.bxcredit.DataDemo;
import org.testng.annotations.Test;

import static org.testng.Assert.assertTrue;

/**
 * Benchmark test for the original DataDemo logic.
 * This test calls the core logic of the DataDemo class to ensure it runs without errors.
 * It serves as a safety net to verify that refactoring does not break the original functionality.
 */
public class DataDemoBenchmarkTest {

    @Test
    public void testExecuteDataDemoLogic() {
        try {
            // We will refactor the core logic of DataDemo.main() into a public static method
            // so that we can call it here directly.
            DataDemo.executeDataDemoLogic();

            // For now, we'll just assert that the method completes without throwing an exception.
            // More specific assertions (e.g., file existence) can be added later.
            assertTrue(true, "DataDemo logic executed successfully.");

        } catch (Exception e) {
            e.printStackTrace();
            assertTrue(false, "DataDemo logic threw an exception: " + e.getMessage());
        }
    }
}
