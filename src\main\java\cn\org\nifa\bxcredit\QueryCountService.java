package cn.org.nifa.bxcredit;

import cn.org.nifa.bxcredit.service.CreditApiClient;
import cn.org.nifa.bxcredit.util.HashUtils;
import cn.org.nifa.bxcredit.util.RandomUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

//[查询量耗用查看接口]
//用于查询最大查询量和当月查询量耗用情况
//生产上是每天更新查询量，每天查询一次即可
@Service
public class QueryCountService {

    private static final Logger logger = LoggerFactory.getLogger(QueryCountService.class);
    private final CreditApiClient creditApiClient;
    private final ObjectMapper objectMapper; // For JSON parsing

    public QueryCountService(CreditApiClient creditApiClient, ObjectMapper objectMapper) {
        this.creditApiClient = creditApiClient;
        this.objectMapper = objectMapper;
    }

    public Mono<String> queryCountData(String stype, String sdate) {
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("stype", stype);
        bodyMap.put("sdate", sdate);
        logger.info("[body]: {}", objectMapper.writeValueAsString(bodyMap)); // Utils.map2Json is still there for now

        // 构造header
        Map<String, String> headerMap = getHeaderMap(bodyMap);
        logger.info("[header]: {}", objectMapper.writeValueAsString(headerMap));

        return creditApiClient.post(Config.QUERY_COUNT_URI, headerMap, bodyMap)
                .doOnSuccess(responseString -> {
                    logger.info("Response: {}", responseString);
                    try {
                        JsonNode jsonNode = objectMapper.readTree(responseString);
                        logger.info("Parsed JSON: {}", jsonNode.toPrettyString());
                    } catch (Exception e) {
                        logger.error("Error parsing JSON response: {}", e.getMessage(), e);
                    }
                })
                .doOnError(e -> logger.error("[ERROR] during query count: {}", e.getMessage(), e));
    }

    private Map<String, String> getHeaderMap(Map<String, String> map) {
        // 构造header
        // header("sbankcode", sbankcode)
        String scode = RandomUtils.getRandomNumber(10); // 10位随机数
        Map<String, String> headerMap = new HashMap<>();
        String preparedSign = Config.ORGCODE
                + scode
                + map.get("sdate")
                + map.get("stype")
                + Config.KEY;
        String sign = HashUtils.sha256(preparedSign);
        headerMap.put("sbankcode", Config.ORGCODE);
        headerMap.put("scode", scode);
        headerMap.put("sign", sign);

        return headerMap;
    }
}