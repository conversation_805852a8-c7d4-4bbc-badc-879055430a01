package cn.org.nifa.bxcredit.service;

import cn.org.nifa.bxcredit.Config;
import cn.org.nifa.bxcredit.Utils;
import org.bouncycastle.util.encoders.Hex;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.testng.Assert.assertEquals;

/**
 * Compatibility test for CryptoService.
 * This test ensures that the new Bouncy Castle-based implementation is fully compatible
 * with the old implementation that uses the proprietary jaf.crypto library.
 */
public class CryptoServiceTest {

    private CryptoService cryptoService;
    private final String testData = "This is the test data for encryption/decryption compatibility check.";
    private final String tempDir = "tmp/";

    @BeforeClass
    public void setUp() {
        cryptoService = new CryptoService();
        new File(tempDir).mkdirs();
    }

    @Test
    public void testEncryptionDecryptionCompatibility() throws Exception {
        // --- Test Case 1: Encrypt with old Utils, Decrypt with new CryptoService ---
        File sourceFile = new File(tempDir + "source1.txt");
        Files.write(Paths.get(sourceFile.toURI()), testData.getBytes());

        String oldEncFile = tempDir + "encrypted_by_old_utils.enc";
        Utils.encryptFile(sourceFile.getAbsolutePath(), oldEncFile);

        byte[] encryptedDataOld = Files.readAllBytes(Paths.get(oldEncFile));
        byte[] decryptedDataNew = cryptoService.sm2Decrypt(encryptedDataOld, Config.PRV_KEY);

        assertEquals(new String(decryptedDataNew), testData, "Decryption by new service should match original data.");

        // --- Test Case 2: Encrypt with new CryptoService, Decrypt with old Utils ---
        String newEncFile = tempDir + "encrypted_by_new_service.enc";
        String fullPublicKey = Config.PUB_X_KEY + Config.PUB_Y_KEY;

        byte[] encryptedDataNew = cryptoService.sm2Encrypt(testData.getBytes(), Config.PUB_X_KEY, Config.PUB_Y_KEY);
        Files.write(Paths.get(newEncFile), encryptedDataNew);

        String decryptedFileOld = tempDir + "decrypted_by_old_utils.txt";
        Utils.deccryptFile(newEncFile, decryptedFileOld);

        String decryptedDataOld = new String(Files.readAllBytes(Paths.get(decryptedFileOld)));

        assertEquals(decryptedDataOld, testData, "Decryption by old utils should match original data.");

        // Clean up temporary files
        new File(oldEncFile).delete();
        new File(newEncFile).delete();
        new File(decryptedFileOld).delete();
        sourceFile.delete();
    }
}
