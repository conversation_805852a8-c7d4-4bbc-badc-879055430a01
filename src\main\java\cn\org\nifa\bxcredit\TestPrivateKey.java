package cn.org.nifa.bxcredit;

import cn.org.nifa.bxcredit.service.CryptoService;
import org.apache.commons.io.FileUtils; // Keep this for file operations
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class TestPrivateKey {

    // IMPORTANT: This is a placeholder for a TEST private key.
    // NEVER hardcode or commit real private keys to version control.
    // In a real application, private keys should be managed securely (e.g., KMS, environment variables).
    private static final String TEST_PRIVATE_KEY_HEX = "YOUR_TEST_PRIVATE_KEY_HEX_HERE"; // Replace with an actual test private key hex string

    public static void main(String[] args) {
        // TODO Auto-generated method stub
        File encryptedFile = new File("D:\\infoshare2\\00CWC00122014050510143200641.enc"); // Encrypted file path
        File decryptedFile = new File("D:\\infoshare2\\00CWC001220140505101432006411.txt"); // Output decrypted file path

        // Instantiate CryptoService (assuming it's not a Spring component in this main method context)
        // If CryptoService is a Spring component, this main method would typically be run within a Spring context.
        // For a standalone utility, we can instantiate it directly.
        CryptoService cryptoService = new CryptoService();

        try {
            // Read encrypted file bytes
            byte[] encryptedBytes = Files.readAllBytes(encryptedFile.toPath());

            // Decrypt the data
            byte[] decryptedBytes = cryptoService.sm2Decrypt(encryptedBytes, TEST_PRIVATE_KEY_HEX);

            // Write decrypted bytes to output file
            FileUtils.writeByteArrayToFile(decryptedFile, decryptedBytes);

            System.out.println("File decrypted successfully to: " + decryptedFile.getAbsolutePath());

        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("Error during decryption: " + e.getMessage());
        }
    }
}