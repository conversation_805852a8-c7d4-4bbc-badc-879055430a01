package cn.org.nifa.bxcredit.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class HashUtils {

    private static final Logger logger = LoggerFactory.getLogger(HashUtils.class);

    /**
     * sha256摘要算法
     * @param string
     * @return
     */
    public static String sha256(String string) {
        String result = null;
        try {
            MessageDigest sha = MessageDigest.getInstance("SHA-256");
            sha.update(string.getBytes("UTF-8"));
            byte b[] = sha.digest();
            int i;
            StringBuffer buf = new StringBuffer("");
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                String temp = Integer.toHexString(i & 0xFF);
                if (temp.length() == 1) {
                    buf.append("0");
                }
                buf.append(temp);
            }
            result = buf.toString();
        } catch (UnsupportedEncodingException e) {
            logger.error("Error in sha256 (UnsupportedEncodingException): {}", e.getMessage(), e);
        } catch (NoSuchAlgorithmException e) {
            logger.error("Error in sha256 (NoSuchAlgorithmException): {}", e.getMessage(), e);
        }
        return result;
    }

    /**
     * 字符串转md5
     * @param string
     * @return
     */
    public static String string2Md5(String string) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(string.getBytes("UTF-8"));
            byte[] encryption = md5.digest();

            StringBuffer strBuf = new StringBuffer();
            for (int i = 0; i < encryption.length; i++) {
                if (Integer.toHexString(0xff & encryption[i]).length() == 1) {
                    strBuf.append("0").append(Integer.toHexString(0xff & encryption[i]));
                } else {
                    strBuf.append(Integer.toHexString(0xff & encryption[i]));
                }
            }
            return strBuf.toString();
        } catch (NoSuchAlgorithmException e) {
            logger.error("Error in string2Md5 (NoSuchAlgorithmException): {}", e.getMessage(), e);
            return "";
        } catch (UnsupportedEncodingException e) {
            logger.error("Error in string2Md5 (UnsupportedEncodingException): {}", e.getMessage(), e);
            return "";
        }
    }
}