package cn.org.nifa.bxcredit;

import cn.org.nifa.bxcredit.service.CreditApiClient;
import cn.org.nifa.bxcredit.service.CryptoService;
import cn.org.nifa.bxcredit.util.HashUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

@Service
public class DataSubmissionService {

    private static final Logger logger = LoggerFactory.getLogger(DataSubmissionService.class);
    private final CreditApiClient creditApiClient;
    private final CryptoService cryptoService; // Assuming CryptoService is also a Spring component or can be instantiated

    private final ObjectMapper objectMapper;

    public DataSubmissionService(CreditApiClient creditApiClient, CryptoService cryptoService, ObjectMapper objectMapper) {
        this.creditApiClient = creditApiClient;
        this.cryptoService = cryptoService;
        this.objectMapper = objectMapper;
    }

    public Mono<String> submitData() {
        // 构造body
        String filename = "2RR3PEGT2202309061715127112";
        String zipFilename = "tmp/" + filename + ".zip";
        String encFilename = "tmp/" + filename + ".enc";

        try {
            File file = encryptFile(zipFilename, encFilename);
            String base64EncString = file2Base64String(file);

            Map<String, String> bodyMap = new HashMap<>();
            bodyMap.put("sdata", base64EncString);
            logger.info("[preparedMap]: {}", objectMapper.writeValueAsString(bodyMap));

            Map<String, String> headerMap = getHeaderMap(bodyMap, filename);
            logger.info("[header]: {}", objectMapper.writeValueAsString(headerMap));

            return creditApiClient.post(Config.DATA_URI, headerMap, bodyMap)
                    .doOnSuccess(response -> logger.info("Response: {}", response))
                    .doOnError(e -> logger.error("[ERROR] during data submission: {}", e.getMessage(), e));

        } catch (Exception e) {
            logger.error("[ERROR] todo: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    private File encryptFile(String sourceFile, String newFile) throws Exception {
        File file2 = null;
        try {
            byte[] fileBytes = Files.readAllBytes(Paths.get(sourceFile));
            // Assuming Config.PUB_X_KEY and Config.PUB_Y_KEY are accessible
            byte[] encryptedBytes = cryptoService.sm2Encrypt(fileBytes, Config.PUB_X_KEY, Config.PUB_Y_KEY);

            file2 = new File(newFile);
            if (!file2.exists()) {
                file2.createNewFile();
            }
            Files.write(Paths.get(file2.toURI()), encryptedBytes);
        } catch (Exception e) {
            logger.error("Error encrypting file: {}", e.getMessage(), e);
            throw e;
        }
        return file2;
    }

    private String file2Base64String(File file) throws java.io.IOException {
        try (java.io.FileInputStream inputFile = new java.io.FileInputStream(file)) {
            byte[] buffer = new byte[(int) file.length()];
            inputFile.read(buffer);
            return new String(org.apache.commons.codec.binary.Base64.encodeBase64(buffer));
        }
    }

    private Map<String, String> getHeaderMap(Map<String, String> map, String filename) {
        String scode = HashUtils.string2Md5(map.get("sdata"));
        String preparedSign = Config.ORGCODE
                + filename
                + scode
                + Config.KEY;
        String sign = HashUtils.sha256(preparedSign);
        sign = sign.trim().replace("\n", "").replace("\r", "");

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("sbankcode", Config.ORGCODE);
        headerMap.put("sdatacode", filename);
        headerMap.put("scode", scode);
        headerMap.put("sign", sign);
        return headerMap;
    }

    
}