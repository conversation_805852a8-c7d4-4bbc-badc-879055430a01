package cn.org.nifa.bxcredit;

import cn.org.nifa.bxcredit.service.CreditApiClient;
import cn.org.nifa.bxcredit.util.HashUtils;
import cn.org.nifa.bxcredit.util.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import reactor.core.publisher.Mono;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectMapper;

// [查询接口]
@Service
public class HighcourtQueryService {

    private static final Logger logger = LoggerFactory.getLogger(HighcourtQueryService.class);
    private final CreditApiClient creditApiClient;

    private final ObjectMapper objectMapper;

    public HighcourtQueryService(CreditApiClient creditApiClient, ObjectMapper objectMapper) {
        this.creditApiClient = creditApiClient;
        this.objectMapper = objectMapper;
    }

    public Mono<String> queryHighcourtData(String sname, String stype, String sreason, String sno) {
        // 构造body
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("sname", sname);
        bodyMap.put("stype", stype);
        bodyMap.put("sreason", sreason);
        bodyMap.put("sno", sno);
        logger.info("[body]: {}", objectMapper.writeValueAsString(bodyMap));

        // 构造header
        Map<String, String> headerMap = getHeaderMap(bodyMap);
        logger.info("[header]: {}", objectMapper.writeValueAsString(headerMap));

        return creditApiClient.post(Config.HIGHCOURT_URI, headerMap, bodyMap)
                .doOnSuccess(response -> logger.info("Response: {}", response))
                .doOnError(e -> logger.error("[ERROR] during highcourt query: {}", e.getMessage(), e));
    }

    private Map<String, String> getHeaderMap(Map<String, String> map) {
        String scode = RandomUtils.getRandomNumber(10); // 10位随机数
        // 构造签名 用于身份验证
        String preparedSign = Config.ORGCODE
                + scode
                + map.get("sname")
                + map.get("stype")
                + map.get("sreason")
                + map.get("sno")
                + Config.KEY;
        String sign = HashUtils.sha256(preparedSign);
        // 构造header
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("sbankcode", Config.ORGCODE);
        headerMap.put("scode", scode);
        headerMap.put("sign", sign);
        return headerMap;
    }
}