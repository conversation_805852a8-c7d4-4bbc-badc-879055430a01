package cn.org.nifa.bxcredit;

import cn.org.nifa.bxcredit.service.CreditApiClient;
import cn.org.nifa.bxcredit.util.HashUtils;
import cn.org.nifa.bxcredit.util.RandomUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

// [查询接口]
@Service
public class InfoQueryService {

    private static final Logger logger = LoggerFactory.getLogger(InfoQueryService.class);
    private final CreditApiClient creditApiClient;

    private final ObjectMapper objectMapper;

    public InfoQueryService(CreditApiClient creditApiClient, ObjectMapper objectMapper) {
        this.creditApiClient = creditApiClient;
        this.objectMapper = objectMapper;
    }

    public Mono<String> queryInfoData(String sname, String stype, String sreason, String sno) {
        // 构造 body
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("sreason", sreason);
        bodyMap.put("sname", sname);
        bodyMap.put("stype", stype);
        bodyMap.put("sno", sno);
        logger.info("[body]: {}", objectMapper.writeValueAsString(bodyMap)); // Utils.map2Json is still there for now

        // 构造 header
        Map<String, String> headerMap = getHeaderMap(bodyMap);
        logger.info("[header]: {}", objectMapper.writeValueAsString(headerMap));

        return creditApiClient.post(Config.INFO_URI_test, headerMap, bodyMap)
                .doOnSuccess(response -> logger.info("Response: {}", response))
                .doOnError(e -> logger.error("[ERROR] during info query: {}", e.getMessage(), e));
    }

    private Map<String, String> getHeaderMap(Map<String, String> map) {
        String scode = RandomUtils.getRandomNumber(10); // 10 位随机数
        // 构造签名 用于身份验证
        String preparedSign = Config.ORGCODE
                + scode
                + map.get("sname")
                + map.get("stype")
                + map.get("sreason")
                + map.get("sno")
                + Config.KEY;
        String sign = HashUtils.sha256(preparedSign);
        // 构造 header
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("sbankcode", Config.ORGCODE);
        headerMap.put("scode", scode);
        headerMap.put("sign", sign);
        return headerMap;
    }
}