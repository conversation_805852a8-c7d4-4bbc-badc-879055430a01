package cn.org.nifa.bxcredit;

import cn.org.nifa.bxcredit.service.CreditApiClient;
import cn.org.nifa.bxcredit.service.CryptoService;
import cn.org.nifa.bxcredit.util.HashUtils;
import cn.org.nifa.bxcredit.util.RandomUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

//[报送状态查看接口]
//用于查看已报送数据的处理状态 处理状态如：等待处理、正在处理、处理完毕有反馈、处理完毕无反馈
//数据处理时间5~30分钟不等，一般数据报送1小时后调用本接口即可
@Service
public class TaskStatusService {

    private static final Logger logger = LoggerFactory.getLogger(TaskStatusService.class);
    private final CreditApiClient creditApiClient;
    private final ObjectMapper objectMapper;
    private final CryptoService cryptoService;

    // IMPORTANT: This is a placeholder for a TEST private key.
    // NEVER hardcode or commit real private keys to version control.
    // In a real application, private keys should be managed securely (e.g., KMS, environment variables).
    private static final String TEST_PRIVATE_KEY_HEX = "YOUR_TEST_PRIVATE_KEY_HEX_HERE"; // Replace with an actual test private key hex string

    public TaskStatusService(CreditApiClient creditApiClient, ObjectMapper objectMapper, CryptoService cryptoService) {
        this.creditApiClient = creditApiClient;
        this.objectMapper = objectMapper;
        this.cryptoService = cryptoService;
    }

    public Mono<String> queryTaskStatus(String sdatacode) {
        // 构造body
        Map<String, String> preparedMap = new HashMap<>();
        preparedMap.put("sdatacode", sdatacode);
        logger.info("[preparedMap]: {}", objectMapper.writeValueAsString(preparedMap)); // Utils.map2Json is still there for now

        // 构造header
        Map<String, String> headerMap = getHeaderMap(preparedMap);
        logger.info("[header]: {}", objectMapper.writeValueAsString(headerMap));

        Map<String, String> bodyMap = new HashMap<>(); // Original code had an empty bodyMap for post
        return creditApiClient.post(Config.TASK_URI, headerMap, bodyMap)
                .doOnSuccess(responseString -> {
                    logger.info("Response: {}", responseString);
                    try {
                        JsonNode json = objectMapper.readTree(responseString);
                        // 如果有反馈文件 说明有数据未成功入库
                        // 反馈报文保存为txt文件
                        if (json.has("returnresultdata") && json.get("returnresultdata") != null && !json.get("returnresultdata").isNull()) {
                            File decEncFile = new File("tmp/test.enc"); // 解密enc文件
                            try {
                                FileUtils.writeByteArrayToFile(decEncFile, Base64.decodeBase64(json.get("returnresultdata").asText()));
                                // 解密结果。里面的错误代码从《采集标准》查询
                                byte[] encryptedBytes = FileUtils.readFileToByteArray(decEncFile);
                                byte[] decryptedBytes = cryptoService.sm2Decrypt(encryptedBytes, TEST_PRIVATE_KEY_HEX);
                                FileUtils.writeByteArrayToFile(new File("tmp/test.txt"), decryptedBytes);

                            } catch (IOException e) {
                                logger.error("Error writing/reading file for decryption: {}", e.getMessage(), e);
                            } catch (Exception e) {
                                logger.error("Error during decryption: {}", e.getMessage(), e);
                            } finally {
                                decEncFile.delete();
                            }
                            logger.info("存在入库失败的数据，见tmp/test.txt");
                        }
                    } catch (Exception e) {
                        logger.error("Error parsing JSON response or processing returnresultdata: {}", e.getMessage(), e);
                    }
                })
                .doOnError(e -> logger.error("[ERROR] during task status query: {}", e.getMessage(), e));
    }

    private Map<String, String> getHeaderMap(Map<String, String> map) {
        //
        String scode = RandomUtils.getRandomNumber(10); // 10位随机数
        // 构造签名 用于身份验证
        // sbankcode + scode + sdatacode + "cfcc";
        String preparedSign = Config.ORGCODE // sbankcode
                + scode //scode
                + map.get("sdatacode")  //sdatacode
                + Config.KEY;  //key
        String sign = HashUtils.sha256(preparedSign);
        // 构造header
        // header("sbankcode", sbankcode).header("scode", scode).header("sign", sign).header("sdatacode", sdatacode)
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("sbankcode", Config.ORGCODE);
        headerMap.put("scode", scode);
        headerMap.put("sign", sign);
        headerMap.put("sdatacode", map.get("sdatacode"));
        return headerMap;
    }
}