package cn.org.nifa.bxcredit.service;

import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECKeyGenerationParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.security.Security;

/**
 * Service responsible for all cryptographic operations.
 * This class encapsulates the logic for SM2/SM3/SM4 algorithms using Bouncy Castle.
 */
@Service
public class CryptoService {

    private static final String SM2_CURVE_NAME = "sm2p256v1";
    private static final ECDomainParameters sm2DomainParams;

    static {
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
        ECParameterSpec ecParameterSpec = org.bouncycastle.jce.ECNamedCurveTable.getParameterSpec(SM2_CURVE_NAME);
        sm2DomainParams = new ECDomainParameters(ecParameterSpec.getCurve(), ecParameterSpec.getG(), ecParameterSpec.getN(), ecParameterSpec.getH());
    }

    /**
     * Encrypts data using the SM2 algorithm with the provided public key.
     *
     * @param plainData The byte array of the data to encrypt.
     * @param pubKeyHex The public key as a hex string (uncompressed format, 04 prefix).
     * @return The encrypted data as a byte array.
     * @throws Exception if encryption fails.
     */
    public byte[] sm2Encrypt(byte[] plainData, String pubXHex, String pubYHex) throws Exception {
        ECPublicKeyParameters publicKeyParameters = getPublicKeyParametersFromXYHex(pubXHex, pubYHex);
        SM2Engine sm2Engine = new SM2Engine();
        sm2Engine.init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));
        return sm2Engine.processBlock(plainData, 0, plainData.length);
    }

    /**
     * Decrypts data using the SM2 algorithm with the provided private key.
     *
     * @param encryptedData The byte array of the encrypted data.
     * @param privKeyHex The private key as a hex string.
     * @return The decrypted data as a byte array.
     * @throws Exception if decryption fails.
     */
    public byte[] sm2Decrypt(byte[] encryptedData, String privKeyHex) throws Exception {
        ECPrivateKeyParameters privateKeyParameters = getPrivateKeyParametersFromHex(privKeyHex);
        SM2Engine sm2Engine = new SM2Engine();
        sm2Engine.init(false, privateKeyParameters);
        return sm2Engine.processBlock(encryptedData, 0, encryptedData.length);
    }

    private ECPublicKeyParameters getPublicKeyParametersFromXYHex(String pubXHex, String pubYHex) {
        BigInteger x = new BigInteger(pubXHex, 16);
        BigInteger y = new BigInteger(pubYHex, 16);
        ECPoint ecPoint = sm2DomainParams.getCurve().createPoint(x, y);
        return new ECPublicKeyParameters(ecPoint, sm2DomainParams);
    }

    private ECPrivateKeyParameters getPrivateKeyParametersFromHex(String hex) {
        BigInteger privateKey = new BigInteger(1, Hex.decode(hex));
        return new ECPrivateKeyParameters(privateKey, sm2DomainParams);
    }

    // ... other crypto methods (sign, verify, hash) will be implemented here
}