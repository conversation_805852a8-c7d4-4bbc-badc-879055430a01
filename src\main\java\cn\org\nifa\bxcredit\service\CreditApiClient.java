package cn.org.nifa.bxcredit.service;

import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import java.util.Map;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpHeaders;

@Service
public class CreditApiClient {

    private final WebClient webClient;

    public CreditApiClient(WebClient.Builder webClientBuilder) {
        // Base URL can be configured via application.properties/yaml
        // For now, we'll assume it's passed or configured elsewhere.
        // For example: this.webClient = webClientBuilder.baseUrl("http://localhost:8080").build();
        this.webClient = webClientBuilder.build();
    }

    public Mono<String> post(String uri, Map<String, String> headers, Map<String, String> body) {
        return webClient.post()
                .uri(uri)
                .headers(httpHeaders -> headers.forEach(httpHeaders::add))
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class);
    }
}