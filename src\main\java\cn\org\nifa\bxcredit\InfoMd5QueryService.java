package cn.org.nifa.bxcredit;

import cn.org.nifa.bxcredit.service.CreditApiClient;
import cn.org.nifa.bxcredit.util.HashUtils;
import cn.org.nifa.bxcredit.util.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

// [查询接口]
@Service
public class InfoMd5QueryService {

    private static final Logger logger = LoggerFactory.getLogger(InfoMd5QueryService.class);
    private final CreditApiClient creditApiClient;

    private final ObjectMapper objectMapper;

    public InfoMd5QueryService(CreditApiClient creditApiClient, ObjectMapper objectMapper) {
        this.creditApiClient = creditApiClient;
        this.objectMapper = objectMapper;
    }

    public Mono<String> queryInfoMd5Data(String sreason, String name, String idNumber) {
        // 构造body
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("sreason", sreason);
        // 企业共享查询接口-密文
        // bodyMap.put("md5", HashUtils.string2Md5(HashUtils.string2Md5(name) + "password") + "0" + HashUtils.string2Md5(HashUtils.string2Md5(idNumber) + "password"));
        // 示例：使用传入的name和idNumber构造md5
        bodyMap.put("md5", HashUtils.string2Md5(HashUtils.string2Md5(name + "password") + "0" + HashUtils.string2Md5(idNumber + "password")));

        logger.info("[body]: {}", objectMapper.writeValueAsString(bodyMap)); // Utils.map2Json is still there for now

        // 构造header
        Map<String, String> headerMap = getHeaderMap(bodyMap);
        logger.info("[header]: {}", objectMapper.writeValueAsString(headerMap));

        return creditApiClient.post(Config.INFO_URI_MD5, headerMap, bodyMap)
                .doOnSuccess(response -> logger.info("Response: {}", response))
                .doOnError(e -> logger.error("[ERROR] during info MD5 query: {}", e.getMessage(), e));
    }

    private Map<String, String> getHeaderMap(Map<String, String> map) {
        String scode = RandomUtils.getRandomNumber(10); // 10位随机数
        // 构造签名 用于身份验证
        String preparedSign = Config.ORGCODE
                + scode
                + map.get("md5")
                + map.get("sreason")
                + Config.KEY;
        String sign = HashUtils.sha256(preparedSign);
        // 构造header
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("sbankcode", Config.ORGCODE);
        headerMap.put("scode", scode);
        headerMap.put("sign", sign);
        return headerMap;
    }
}