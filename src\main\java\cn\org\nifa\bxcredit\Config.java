package cn.org.nifa.bxcredit;

// 配置参数
public class Config {
	/* 组织机构代码
	* ！注意采用的是9位代码，计算方式： 统一社会信用代码的9-17位，开通接口的《回执》中会提供
	*/


	public static final String ORGCODE = "C1B476EA0";  // 必须修改
	/* 接口key
	* 由协会在开通接口时发放，联调环境与生产环境采用不同的key
	* 十分重要，生产参数务必妥善保存
	*/

	/*青岛钱吧  组织机构代码  097967874
	* 青岛钱吧 密钥  mNak7HBrKFFXMn72w5
	* 中建创业科技有限公司  C1B476EA0
	* 中建创业科技有限公司  aTRPQZX5ZEWFpTix5j
	*
	* */
	public static final String KEY = "aTRPQZX5ZEWFpTix5j";  // 必须修改

	// [查询接口]
		//个人借贷共享查 询接口-密文（含保险） INFO_URI_MD5
	public static final String INFO_URI_MD5= "https://credit.nifa.org.cn/NifaCreditServer/context/info/json/uploadnew";
		//个人借贷共享查 询接口-明文（含保险） INFO_URI
//	个人借贷共享查询接口-生产
//	public static final String INFO_URI_product = "https://credit.nifa.org.cn/NifaCreditServer/context/info/json/upload";
//	个人借贷共享查询接口-测试
    public static final String INFO_URI_test = "https://credit2pre.nifa.org.cn/NewNifaServer/context/info/json/upload";

//	开发环境地址
//	public static final String INFO_URI_test = "http://172.18.12.178/NifaServer/context/info/json/upload";

//	public static final String INFO_URI_test = "http://172.18.12.178/NifaServer/context/info/json/upload";
		//企业共享查询接口-密文
	//public static final String INFO_URI_MD5 = "https://credit.nifa.org.cn/NifaCreditServer/context/info/json/enterprisenew";
		//企业共享查询接口-明文
	//public static final String INFO_URI = "https://credit.nifa.org.cn/NifaCreditServer/context/info/json/enterprise";
	
	// [司法数据查询接口]
//	public static final String HIGHCOURT_URI = "https://credit.nifa.org.cn/NifaCreditServer/nifa/sxzbr/json/people";
	public static final String HIGHCOURT_URI = "http://172.18.13.78:81//highCourt/nifa/sxzbr/json/people";
	
	// [数据上报接口]
	public static final String DATA_URI = "https://testcredit2.nifa.org.cn/NewNifaServer1/context/data/json/asynlist";
	
	// [报送任务查询接口]
	public static final String TASK_URI = "https://testcredit2.nifa.org.cn/NewNifaServer1/context/task/json/upload";
	
	// [查询量耗用情况查询接口]
	public static final String QUERY_COUNT_URI = "https://testcredit2.nifa.org.cn/NewNifaServer/context/info/json/querycontralNew";

	// 报送文件加密用公钥1 测试联调环境和生产环境通用
	public static final String PUB_X_KEY = "da68acf0ae676725fbd70894dfe7aaac5af008009bc30c13daf4e691f575d12a";
	// 报送文件加密用公钥2 测试联调环境和生产环境通用
	public static final String PUB_Y_KEY = "76d4a0f90065ad86221287a74bf99862e92124282dba02b94782ff50f8ea6701";
	// 反馈文件解密用私钥 测试联调环境和生产环境通用
	public static final String PRV_KEY = "143c18f085e49697b7918d1a03a90c49bbe8ca1b741511bbac9e81cceb7563d5";

}
